<template>
  <div class="pdf-viewer">
    <div class="header">
      <h2>PDF预览</h2>
      <button @click="loadPdf" class="load-btn" :disabled="loading">
        {{ loading ? '加载中...' : '加载PDF' }}
      </button>
    </div>
    
    <div class="pdf-container" ref="container">
      <div v-if="loading" class="loading">
        <div>正在加载PDF...</div>
      </div>
      
      <div v-else-if="!pdfUrl" class="empty">
        <div>点击按钮加载PDF</div>
      </div>
      
      <!-- PDF页面将在这里渲染 -->
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onUnmounted } from 'vue'
import * as pdfjsLib from 'pdfjs-dist'
import pdfWorker from 'pdfjs-dist/build/pdf.worker?url'
import axios from 'axios'

pdfjsLib.GlobalWorkerOptions.workerSrc = pdfWorker

const container = ref(null)
const loading = ref(false)
const pdfUrl = ref('')

const loadPdf = async () => {
  loading.value = true
  
  try {
    // 请求后端PDF文件流
    const response = await axios.get('http://**************:8000/api/v1/pba/common/showPdf', {
      responseType: 'blob',
      headers: {
        'Accept': 'application/pdf'
      }
    })
    
    // 创建blob URL
    const blob = new Blob([response.data], { type: 'application/pdf' })
    pdfUrl.value = URL.createObjectURL(blob)
    
    // 先设置loading为false，让容器显示
    loading.value = false
    
    // 等待DOM更新
    await nextTick()
    
    // 渲染PDF
    await renderPdf(pdfUrl.value)
    
  } catch (error) {
    console.error('加载PDF失败:', error)
    alert('加载PDF失败，请重试')
    loading.value = false
  }
}

const renderPdf = async (url) => {
  // 检查容器是否存在
  if (!container.value) {
    console.error('PDF容器未找到')
    return
  }
  
  // 清空容器中的加载和空状态元素，保留PDF内容
  const loadingEl = container.value.querySelector('.loading')
  const emptyEl = container.value.querySelector('.empty')
  if (loadingEl) loadingEl.remove()
  if (emptyEl) emptyEl.remove()
  
  // 清空之前的PDF内容
  const canvases = container.value.querySelectorAll('canvas')
  canvases.forEach(canvas => canvas.remove())
  
  try {
    const loadingTask = pdfjsLib.getDocument(url)
    const pdf = await loadingTask.promise
    const devicePixelRatio = window.devicePixelRatio || 1

    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      const page = await pdf.getPage(pageNum)

      // 计算适合屏幕的缩放比例
      const containerWidth = container.value.clientWidth - 20
      const viewport = page.getViewport({ scale: 1 })
      const scale = Math.min(containerWidth / viewport.width, 1.2)
      const scaledViewport = page.getViewport({ scale })

      // 创建canvas
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')

      // 设置canvas尺寸
      canvas.width = scaledViewport.width * devicePixelRatio
      canvas.height = scaledViewport.height * devicePixelRatio
      canvas.style.width = scaledViewport.width + 'px'
      canvas.style.height = scaledViewport.height + 'px'
      canvas.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)'
      canvas.style.borderRadius = '4px'
      canvas.style.backgroundColor = 'white'
      canvas.style.marginBottom = '15px'
      context.setTransform(devicePixelRatio, 0, 0, devicePixelRatio, 0, 0)

      container.value.appendChild(canvas)

      // 渲染页面
      await page.render({
        canvasContext: context,
        viewport: scaledViewport,
      }).promise
    }
  } catch (error) {
    console.error('渲染PDF失败:', error)
    alert('渲染PDF失败')
  }
}

// 组件销毁时清理URL
onUnmounted(() => {
  if (pdfUrl.value) {
    URL.revokeObjectURL(pdfUrl.value)
  }
})
</script>

<style scoped lang="scss">
.pdf-viewer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  h2 {
    margin: 0;
    color: #333;
  }
}

.load-btn {
  background-color: #8b0001;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  
  &:hover:not(:disabled) {
    background-color: #a00001;
  }
  
  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
}

.pdf-container {
  flex: 1;
  overflow: auto;
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.loading, .empty {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #666;
  font-size: 16px;
  width: 100%;
}
</style>